# 🎯 自定义动作功能使用指南

## 功能概述

自定义动作功能允许您创建、管理和使用自己的动作模板，支持两种创建方式：
- **📷 摄像头截图**: 实时捕获姿势并提取关键点
- **📁 图片上传**: 上传图片并进行姿态检测

## 🚀 快速开始

### 1. 创建自定义动作

1. 在主界面右侧找到"自定义动作"区域
2. 点击"➕ 创建动作"按钮
3. 选择创建方式：
   - **摄像头截图**: 适合实时创建，需要先启用摄像头
   - **图片上传**: 适合使用现有图片，支持JPG、PNG、WebP格式

### 2. 编辑关键点

创建动作后会进入关键点编辑界面：
- **拖拽调整**: 直接拖拽关键点调整位置
- **右键删除**: 右键点击关键点进行删除
- **手动添加**: 点击"➕ 手动添加关键点"按钮，然后在图片上点击添加
- **自动检测**: 点击"🔍 尝试自动检测"按钮（仅摄像头截图推荐使用）

### 3. 保存动作

1. 填写动作信息：
   - **动作名称**: 必填，用于识别动作
   - **动作描述**: 可选，描述动作要点
   - **难度等级**: 1-5级，影响评分标准
2. 点击"💾 保存动作"完成创建

### 4. 使用自定义动作

1. 在动作选择器中找到您的自定义动作（标有"🎯 (自定义)"）
2. 选择后开始检测，系统会根据您定义的关键点进行评分

## 📋 管理自定义动作

### 打开管理界面

点击"⚙️ 管理动作"按钮打开专门的管理界面

### 管理功能

- **👁️ 预览**: 查看动作详细信息和参考图片
- **✏️ 编辑**: 修改动作信息（开发中）
- **📋 复制**: 创建动作副本
- **🗑️ 删除**: 删除不需要的动作
- **📁 导入**: 从文件导入动作数据
- **💾 导出**: 导出动作数据到文件
- **🗑️ 清空全部**: 删除所有自定义动作

## 💡 使用技巧

### 创建高质量动作

1. **关键点建议**:
   - 至少标注主要关节点：头部、肩膀、手肘、手腕、臀部、膝盖、脚踝
   - 确保关键点位置准确，这直接影响识别效果

2. **摄像头截图技巧**:
   - 确保光线充足，背景简洁
   - 姿势标准，关节点清晰可见
   - 建议正面或侧面拍摄

3. **图片上传技巧**:
   - 使用高质量图片（建议分辨率不超过1920x1080）
   - 人物在图片中占比适中
   - 避免复杂背景干扰

### 优化识别效果

1. **关键点质量**:
   - 确保关键点数量充足（建议8个以上）
   - 关键点分布均匀，覆盖主要身体部位
   - 避免关键点重叠或位置错误

2. **动作设计**:
   - 选择具有明显特征的姿势
   - 避免过于复杂或模糊的动作
   - 考虑动作的可重复性和稳定性

## ⚠️ 注意事项

### 技术限制

1. **图片姿态检测**:
   - 图片自动姿态检测功能有限，主要依赖手动添加关键点
   - **强烈建议使用摄像头截图功能**，可以自动检测关键点
   - 上传图片后需要手动添加和调整关键点

2. **浏览器兼容性**:
   - 需要现代浏览器支持（Chrome、Firefox、Safari、Edge）
   - 需要摄像头权限（用于截图功能）
   - 需要支持Canvas、FileReader等API

3. **存储限制**:
   - 数据存储在浏览器本地（localStorage）
   - 清除浏览器数据会丢失自定义动作
   - 建议定期导出备份重要动作

### 性能建议

1. **文件大小**: 图片文件建议不超过10MB
2. **图片尺寸**: 建议不超过1920x1080像素
3. **动作数量**: 建议不超过50个自定义动作

## 🔧 故障排除

### 常见问题

1. **摄像头无法启用**:
   - 检查浏览器权限设置
   - 确保摄像头未被其他应用占用
   - 尝试刷新页面重新授权

2. **图片上传失败**:
   - 检查文件格式是否支持
   - 确认文件大小不超过限制
   - 尝试使用其他图片

3. **姿态检测失败**:
   - 确保图片中人物清晰可见
   - 尝试使用摄像头截图替代
   - 手动添加和调整关键点

4. **动作保存失败**:
   - 确保动作名称不为空
   - 检查是否有足够的关键点
   - 尝试清除浏览器缓存

### 调试工具

访问 `/debug-test.html` 页面可以进行功能调试和问题诊断。

## 📞 技术支持

如果遇到问题或需要帮助，请：
1. 检查浏览器控制台是否有错误信息
2. 尝试使用调试页面诊断问题
3. 记录具体的操作步骤和错误信息

## 🎉 享受创作

现在您可以开始创建属于自己的动作库了！发挥创意，制作独特的动作模板，让运动更加个性化和有趣。
