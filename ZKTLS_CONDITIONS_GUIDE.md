# zkTLS 证明条件完整指南

## 🎯 问题解决方案

### 原始错误分析
错误信息："证明生成失败: 未满足证明条件: score, duration, accuracy"

**根本原因**：
1. **数据映射错误**：zkTLS 系统期望 `duration`（实际秒数），但收到的是 `durationScore`（百分比）
2. **阈值设置过高**：默认要求可能超出实际应用场景
3. **缺乏实时监控**：无法看到当前数值与要求的对比

## 📊 当前证明条件要求

### 默认阈值（已修复）
```javascript
conditions: {
    minScore: 75,        // 最低总分：75分
    minDuration: 3,      // 最低持续时间：3秒
    minAccuracy: 80,     // 最低准确度：80%
}
```

### 数据来源说明
- **分数 (score)**：来自 `scoreData.currentScore`（综合评分 0-100）
- **持续时间 (duration)**：来自 `scoreData.holdTime`（实际秒数，已修复）
- **准确度 (accuracy)**：来自 `scoreData.accuracy`（准确度百分比 0-100）

## 🎮 如何满足证明条件

### 1. 达到最低分数 (75分)
**分数计算公式**：
```
总分 = 准确度 × 50% + 稳定性 × 30% + 持续时间分数 × 20%
```

**提升策略**：
- 🎯 **提高准确度**：确保身体姿势与目标动作匹配
- 🧘 **保持稳定**：避免频繁移动，保持姿势稳定
- ⏱️ **延长持续时间**：保持动作直到达到目标时长

### 2. 满足持续时间要求 (3秒)
**计时机制**：
- 只有当准确度 ≥ 70% 时才开始计时
- 准确度低于阈值时计时器重置
- 需要连续保持高准确度 3 秒以上

**实用技巧**：
- 📍 **找准位置**：调整身体位置确保摄像头能清晰检测关键点
- 🎯 **精确姿势**：参考动作指导，确保姿势标准
- 💪 **保持稳定**：避免晃动，保持姿势稳定

### 3. 达到准确度要求 (80%)
**准确度影响因素**：
- 关键点检测质量
- 姿势与标准动作的匹配度
- 光照条件和摄像头角度

**优化建议**：
- 💡 **改善光照**：确保充足且均匀的光线
- 📐 **调整角度**：保持摄像头水平，身体居中
- 👕 **穿着建议**：避免宽松衣物影响关键点检测

## 🔧 实时监控工具

### 1. zkTLS 调试监控器
**自动启用**：开始检测时自动显示在右上角

**功能特性**：
- 📊 实时显示当前分数、持续时间、准确度
- ✅ 条件满足状态指示器
- 💡 个性化改进建议
- ⚙️ 实时阈值调整

**使用方法**：
1. 启动姿态检测
2. 观察右上角调试面板
3. 根据提示调整姿势
4. 等待所有指示器变为 ✅

### 2. 条件测试工具
**访问地址**：`test-zktls-conditions.html`

**功能**：
- 🧪 模拟不同数值组合
- ⚙️ 调整证明条件阈值
- 📈 可视化结果展示
- 💡 智能建议系统

## 🎯 实际操作指南

### 步骤 1：准备环境
```bash
# 确保开发服务器运行
npm run dev
```

### 步骤 2：初始化 zkTLS
1. 打开主应用：`http://localhost:8080`
2. 点击"🔗 连接钱包"
3. 点击"🚀 初始化zkTLS"
4. 等待状态显示"已就绪"

### 步骤 3：开始检测
1. 点击"启用摄像头"
2. 选择要练习的动作
3. 点击"开始检测"
4. 观察右上角调试面板

### 步骤 4：满足条件
**针对不同动作的建议**：

#### 树式 (Tree Pose)
- 🦵 **单腿站立**：重心稳定，支撑腿伸直
- 🙏 **手臂位置**：双手合十于胸前或举过头顶
- ⏱️ **保持时间**：稳定保持 3-5 秒
- 🎯 **目标准确度**：≥ 85%

#### Diamond Hands
- 💎 **手型标准**：双手形成钻石形状
- 📐 **肘部角度**：保持 80-120° 角度
- 🧍 **身体姿态**：上身挺直，倾斜度 < 15°
- ⏱️ **保持时间**：稳定保持 5 秒
- 🎯 **目标准确度**：≥ 75%

#### 点赞手势
- 👍 **手势清晰**：拇指向上，其他手指握拳
- 📏 **距离适中**：手部距离摄像头 30-60cm
- 🎯 **目标准确度**：≥ 80%

### 步骤 5：生成证明
1. 等待调试面板显示"✅ 满足证明条件"
2. 点击"📜 生成证明"
3. 等待证明生成完成
4. 查看证明历史记录

## ⚙️ 自定义配置

### 降低难度（测试用）
```javascript
// 在浏览器控制台执行
ZKTLSConfig.conditions.minScore = 60;      // 降低到60分
ZKTLSConfig.conditions.minDuration = 2;    // 降低到2秒
ZKTLSConfig.conditions.minAccuracy = 70;   // 降低到70%
```

### 提高难度（挑战模式）
```javascript
ZKTLSConfig.conditions.minScore = 90;      // 提高到90分
ZKTLSConfig.conditions.minDuration = 5;    // 提高到5秒
ZKTLSConfig.conditions.minAccuracy = 90;   // 提高到90%
```

### 使用条件测试工具
1. 打开 `test-zktls-conditions.html`
2. 调整左侧配置参数
3. 点击"更新配置"
4. 输入测试数值
5. 点击"测试条件"查看结果

## 🐛 常见问题解决

### 问题 1：持续时间始终为 0
**原因**：准确度未达到计时阈值（70%）
**解决**：
- 调整身体位置提高检测准确度
- 确保光照充足
- 检查摄像头是否正常工作

### 问题 2：分数过低
**原因**：姿势不标准或检测不稳定
**解决**：
- 参考动作指导调整姿势
- 保持身体稳定，避免晃动
- 确保关键点清晰可见

### 问题 3：准确度不稳定
**原因**：环境因素或姿势问题
**解决**：
- 改善光照条件
- 调整摄像头角度
- 穿着贴身衣物
- 确保背景简洁

### 问题 4：证明生成失败
**检查清单**：
- [ ] zkTLS 服务已初始化
- [ ] 钱包已连接
- [ ] 所有条件都满足（调试面板显示 ✅）
- [ ] 网络连接正常

## 📈 性能优化建议

### 硬件要求
- **摄像头**：720p 以上分辨率
- **光照**：充足且均匀的环境光
- **网络**：稳定的互联网连接
- **浏览器**：Chrome/Firefox 最新版本

### 环境设置
- 🏠 **空间**：2米 × 2米以上活动空间
- 💡 **光线**：避免背光，确保面部和身体清晰
- 👕 **服装**：贴身、对比度高的衣物
- 📱 **设备**：使用性能较好的设备

## 🎉 成功案例

### 典型成功数值
```
分数: 85-95
持续时间: 3.5-8.0 秒
准确度: 85-95%
```

### 最佳实践
1. **预热练习**：先练习几次熟悉动作
2. **环境检查**：确保光照和空间充足
3. **逐步提升**：从简单动作开始，逐步挑战
4. **耐心等待**：给系统足够时间检测和计算

---

通过以上指南，您应该能够成功生成 zkTLS 证明。如有问题，请使用调试工具进行诊断，或查看浏览器控制台获取详细错误信息。
