# 🔧 故障排除指南

## 常见问题及解决方案

### 1. "ml5.poseNet is not a function" 错误

#### 问题原因
- ml5.js库未正确加载
- 使用了不兼容的ml5.js版本
- 网络连接问题导致库加载失败

#### 解决步骤

1. **检查库加载状态**
   - 打开浏览器开发者工具 (F12)
   - 在控制台输入 `typeof ml5` 检查 ml5.js 是否加载
   - 输入 `typeof ml5.poseNet` 检查 PoseNet 功能是否可用

2. **检查网络连接**
   - 确保能访问CDN (unpkg.com, cdnjs.com)
   - 尝试刷新页面重新加载库

3. **检查浏览器控制台**
   - 按F12打开开发者工具
   - 查看Console标签页的错误信息
   - 查看Network标签页确认库文件加载成功

#### 版本兼容性
- 推荐使用: ml5.js v0.12.2
- 确保p5.js版本兼容 (v1.7.0)
- 注意：不要同时加载 TensorFlow.js，会导致库冲突

### 2. "TypeError: d is not a function" 错误

#### 问题原因
- TensorFlow.js 与 ml5.js 版本冲突
- 同时加载了多个机器学习库

#### 解决步骤
1. **移除 TensorFlow.js 库**
   - 检查 HTML 中是否同时引入了 TensorFlow.js 和 ml5.js
   - 只保留 ml5.js 和 p5.js

2. **清除浏览器缓存**
   - 按 Ctrl+Shift+R 强制刷新页面
   - 或清除浏览器缓存后重新访问

### 3. 摄像头无法启动

#### 问题原因
- 浏览器权限被拒绝
- 摄像头被其他应用占用
- 非安全环境 (需要HTTPS)

#### 解决步骤

1. **检查浏览器权限**
   - 点击地址栏的摄像头图标
   - 确保允许摄像头访问
   - 刷新页面重试

2. **检查摄像头占用**
   - 关闭其他使用摄像头的应用
   - 重启浏览器

3. **确保安全环境**
   - 使用 localhost 或 127.0.0.1
   - 或使用HTTPS访问

### 3. AI模型加载缓慢

#### 问题原因
- 网络速度慢
- 模型文件较大
- 设备性能限制

#### 解决步骤

1. **等待加载完成**
   - PoseNet模型约10-20MB
   - 首次加载需要更多时间

2. **检查网络状态**
   - 确保网络连接稳定
   - 避免同时下载其他大文件

3. **降低模型复杂度**
   - 调整imageScaleFactor参数
   - 减少maxPoseDetections

### 4. 检测精度不高

#### 问题原因
- 光线条件不佳
- 背景复杂
- 距离摄像头太远/太近

#### 解决步骤

1. **优化环境条件**
   - 确保充足均匀的光线
   - 选择简洁的背景
   - 保持1-2米的距离

2. **调整检测参数**
   - 降低minConfidence阈值
   - 调整scoreThreshold值

3. **改善动作质量**
   - 做标准、清晰的动作
   - 保持动作稳定2-3秒

### 5. 性能问题

#### 问题原因
- 设备性能不足
- 浏览器资源占用过高
- 检测频率过高

#### 解决步骤

1. **优化浏览器**
   - 关闭不必要的标签页
   - 重启浏览器释放内存

2. **调整检测参数**
   - 增加imageScaleFactor值
   - 降低检测频率

3. **使用性能更好的设备**
   - 推荐使用现代浏览器
   - 确保设备有足够的内存

## 🛠️ 调试工具

### 1. 调试页面
```
http://localhost:3000/debug.html
```
- 检查库加载状态
- 测试PoseNet功能
- 查看系统兼容性

### 2. 简化测试页面
```
http://localhost:3000/simple-pose.html
```
- 基础PoseNet测试
- 最小化功能验证
- 排除复杂逻辑干扰

### 3. 浏览器开发者工具
- **Console**: 查看错误信息和日志
- **Network**: 检查资源加载状态
- **Performance**: 分析性能瓶颈

## 📋 检查清单

在报告问题前，请确认以下项目：

- [ ] 使用现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)
- [ ] 网络连接正常，能访问外部CDN
- [ ] 摄像头权限已允许
- [ ] 没有其他应用占用摄像头
- [ ] 使用localhost或HTTPS访问
- [ ] 已尝试刷新页面
- [ ] 已检查浏览器控制台错误信息
- [ ] 已测试调试页面和简化测试页面

## 🆘 获取帮助

如果问题仍然存在，请提供以下信息：

1. **浏览器信息**: 版本和操作系统
2. **错误信息**: 完整的控制台错误日志
3. **网络状态**: 调试页面的检查结果
4. **重现步骤**: 详细的操作步骤
5. **环境信息**: 是否使用代理、防火墙等

## 🔄 重置方法

如果遇到严重问题，可以尝试完全重置：

1. **清除浏览器缓存**
   - Ctrl+Shift+Delete (Windows)
   - Cmd+Shift+Delete (Mac)

2. **重启服务器**
   ```bash
   # 停止当前服务器 (Ctrl+C)
   # 重新启动
   npx http-server -p 3000 -c-1
   ```

3. **使用隐私模式**
   - 在浏览器隐私/无痕模式下测试
   - 排除扩展程序干扰

---

**记住**: 大多数问题都是由于网络连接或浏览器权限引起的，耐心检查基础环境通常能解决问题。
