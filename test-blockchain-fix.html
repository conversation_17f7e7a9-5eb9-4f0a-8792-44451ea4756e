<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Blockchain Transaction Fixes</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        .test-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .log-area {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .success { color: #2ecc71; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .info { color: #3498db; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Blockchain Transaction Fixes Test</h1>
        <p>This page tests the fixes for gas price conversion and undefined variable errors in smart contract integration.</p>

        <div class="test-section">
            <h3>🔍 Test 1: Gas Calculation Fix</h3>
            <p>Tests that gas calculations produce integer values instead of decimals.</p>
            <button class="test-button" onclick="testGasCalculation()">Test Gas Calculation</button>
            <div id="gasTestLog" class="log-area"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Test 2: Variable Scope Fix</h3>
            <p>Tests that poseData is properly accessible in catch blocks.</p>
            <button class="test-button" onclick="testVariableScope()">Test Variable Scope</button>
            <div id="scopeTestLog" class="log-area"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Test 3: Mock Transaction Test</h3>
            <p>Tests the complete transaction flow with mock data.</p>
            <button class="test-button" onclick="testMockTransaction()" id="mockTestBtn">Test Mock Transaction</button>
            <div id="mockTestLog" class="log-area"></div>
        </div>

        <div class="test-section">
            <h3>📊 Test Results Summary</h3>
            <div id="testSummary" class="log-area">No tests run yet...</div>
        </div>
    </div>

    <!-- Load dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/web3@1.8.0/dist/web3.min.js"></script>
    <script src="js/network-config.js"></script>
    <script src="js/smart-contract-integration.js"></script>

    <script>
        let testResults = {
            gasCalculation: null,
            variableScope: null,
            mockTransaction: null
        };

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        function updateSummary() {
            const summary = document.getElementById('testSummary');
            let summaryText = '📋 Test Results Summary:\n';
            summaryText += '========================\n';
            
            for (const [test, result] of Object.entries(testResults)) {
                const status = result === null ? '⏳ Not run' : 
                              result === true ? '✅ Passed' : '❌ Failed';
                summaryText += `${test}: ${status}\n`;
            }
            
            const allPassed = Object.values(testResults).every(r => r === true);
            const anyFailed = Object.values(testResults).some(r => r === false);
            
            if (allPassed && Object.values(testResults).every(r => r !== null)) {
                summaryText += '\n🎉 All tests passed! Fixes are working correctly.';
            } else if (anyFailed) {
                summaryText += '\n⚠️ Some tests failed. Please check the logs above.';
            }
            
            summary.textContent = summaryText;
        }

        function testGasCalculation() {
            log('gasTestLog', '🧪 Testing gas calculation fixes...', 'info');
            
            try {
                // Test the gas calculation logic that was fixed
                const mockGasEstimate = 416666; // Typical gas estimate
                
                log('gasTestLog', `Original gas estimate: ${mockGasEstimate}`, 'info');
                
                // Test the old problematic calculation
                const oldCalculation = mockGasEstimate * 1.2;
                log('gasTestLog', `Old calculation (problematic): ${oldCalculation}`, 'warning');
                log('gasTestLog', `Is decimal? ${oldCalculation % 1 !== 0}`, 'warning');
                
                // Test the new fixed calculation
                const newCalculation = Math.floor(Math.max(mockGasEstimate * 1.2, 500000));
                log('gasTestLog', `New calculation (fixed): ${newCalculation}`, 'success');
                log('gasTestLog', `Is integer? ${newCalculation % 1 === 0}`, 'success');
                
                // Verify the fix works
                if (newCalculation % 1 === 0 && newCalculation >= 500000) {
                    log('gasTestLog', '✅ Gas calculation fix verified!', 'success');
                    testResults.gasCalculation = true;
                } else {
                    log('gasTestLog', '❌ Gas calculation fix failed!', 'error');
                    testResults.gasCalculation = false;
                }
                
            } catch (error) {
                log('gasTestLog', `❌ Test error: ${error.message}`, 'error');
                testResults.gasCalculation = false;
            }
            
            updateSummary();
        }

        function testVariableScope() {
            log('scopeTestLog', '🧪 Testing variable scope fixes...', 'info');
            
            try {
                // Simulate the fixed function structure
                const mockProof = {
                    poseData: {
                        poseName: 'test-pose',
                        score: 85,
                        duration: 5000
                    }
                };
                
                log('scopeTestLog', 'Testing variable scope in try-catch blocks...', 'info');
                
                // Simulate the fixed structure where poseData is extracted early
                const poseData = mockProof.poseData;
                let poseType = 0;
                
                if (poseData.poseName.includes('hand') || poseData.poseName.includes('gesture')) {
                    poseType = 1;
                } else if (poseData.poseName.includes('custom')) {
                    poseType = 2;
                }
                
                log('scopeTestLog', `Extracted poseData: ${JSON.stringify(poseData)}`, 'info');
                log('scopeTestLog', `Determined poseType: ${poseType}`, 'info');
                
                // Simulate the catch block scenario
                try {
                    throw new Error('Simulated primary transaction failure');
                } catch (error) {
                    log('scopeTestLog', 'Simulating fallback transaction...', 'warning');
                    
                    // Test that poseData is accessible in catch block
                    if (poseData && poseData.poseName && typeof poseData.score === 'number') {
                        log('scopeTestLog', '✅ poseData accessible in catch block!', 'success');
                        log('scopeTestLog', `Fallback would use: ${poseData.poseName}, ${poseType}, ${poseData.score}, ${poseData.duration}`, 'success');
                        testResults.variableScope = true;
                    } else {
                        log('scopeTestLog', '❌ poseData not accessible in catch block!', 'error');
                        testResults.variableScope = false;
                    }
                }
                
            } catch (error) {
                log('scopeTestLog', `❌ Test error: ${error.message}`, 'error');
                testResults.variableScope = false;
            }
            
            updateSummary();
        }

        async function testMockTransaction() {
            const btn = document.getElementById('mockTestBtn');
            btn.disabled = true;
            btn.textContent = 'Testing...';
            
            log('mockTestLog', '🧪 Testing complete transaction flow with mock data...', 'info');
            
            try {
                // Check if SmartContractIntegration is available
                if (typeof SmartContractIntegration === 'undefined') {
                    throw new Error('SmartContractIntegration class not found');
                }
                
                log('mockTestLog', '✅ SmartContractIntegration class found', 'success');
                
                // Create mock proof data
                const mockProof = {
                    id: 'test-proof-' + Date.now(),
                    timestamp: Date.now(),
                    verified: true,
                    poseData: {
                        poseName: 'warrior-pose',
                        score: 92,
                        duration: 8000
                    },
                    attestation: {
                        id: 'test-attestation',
                        templateId: '2e3160ae-8b1e-45e3-8c59-426366278b9d'
                    }
                };
                
                log('mockTestLog', `Mock proof created: ${JSON.stringify(mockProof, null, 2)}`, 'info');
                
                // Test the gas calculation logic
                const gasEstimate = 450000;
                const gasLimit = Math.floor(Math.max(gasEstimate * 1.2, 500000));
                
                log('mockTestLog', `Gas calculation test:`, 'info');
                log('mockTestLog', `  Estimate: ${gasEstimate}`, 'info');
                log('mockTestLog', `  Calculated limit: ${gasLimit}`, 'info');
                log('mockTestLog', `  Is integer: ${gasLimit % 1 === 0}`, gasLimit % 1 === 0 ? 'success' : 'error');
                
                // Test variable extraction
                const poseData = mockProof.poseData;
                let poseType = 0;
                if (poseData.poseName.includes('hand') || poseData.poseName.includes('gesture')) {
                    poseType = 1;
                } else if (poseData.poseName.includes('custom')) {
                    poseType = 2;
                }
                
                log('mockTestLog', `Variable extraction test:`, 'info');
                log('mockTestLog', `  Pose name: ${poseData.poseName}`, 'info');
                log('mockTestLog', `  Pose type: ${poseType}`, 'info');
                log('mockTestLog', `  Score: ${poseData.score}`, 'info');
                log('mockTestLog', `  Duration: ${poseData.duration}`, 'info');
                
                // Test zkProof data preparation
                const zkProofData = JSON.stringify({
                    attestation: {
                        id: mockProof.attestation?.id || mockProof.id,
                        templateId: mockProof.attestation?.templateId || 'rhythm_pose_achievement'
                    },
                    proofId: mockProof.id,
                    timestamp: mockProof.timestamp,
                    verified: mockProof.verified
                });
                
                const maxProofSize = 500;
                const truncatedZkProof = zkProofData.length > maxProofSize ?
                    zkProofData.substring(0, maxProofSize) + '...' : zkProofData;
                
                log('mockTestLog', `zkProof data preparation:`, 'info');
                log('mockTestLog', `  Original size: ${zkProofData.length}`, 'info');
                log('mockTestLog', `  Truncated size: ${truncatedZkProof.length}`, 'info');
                
                log('mockTestLog', '✅ All mock transaction components working correctly!', 'success');
                testResults.mockTransaction = true;
                
            } catch (error) {
                log('mockTestLog', `❌ Mock transaction test failed: ${error.message}`, 'error');
                testResults.mockTransaction = false;
            }
            
            btn.disabled = false;
            btn.textContent = 'Test Mock Transaction';
            updateSummary();
        }

        // Initialize
        updateSummary();
        
        // Auto-run basic tests
        setTimeout(() => {
            testGasCalculation();
            setTimeout(() => {
                testVariableScope();
            }, 1000);
        }, 500);
    </script>
</body>
</html>
