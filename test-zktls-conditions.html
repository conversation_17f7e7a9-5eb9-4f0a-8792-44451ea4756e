<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>zkTLS Conditions Test - Rhythm Pose</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(138, 43, 226, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(138, 43, 226, 0.2);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid rgba(138, 43, 226, 0.2);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.8);
        }
        .section h3 {
            margin-top: 0;
            color: #8A2BE2;
            font-size: 1.2rem;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(138, 43, 226, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
        }
        .btn {
            background: linear-gradient(135deg, #9B4DEE, #8A2BE2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover { 
            background: linear-gradient(135deg, #8A2BE2, #7B68EE);
            transform: translateY(-1px);
        }
        .btn:disabled { 
            background: #E0E0E0;
            cursor: not-allowed;
        }
        .status {
            padding: 12px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: 500;
        }
        .success { 
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }
        .error { 
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.2);
        }
        .info { 
            background: rgba(23, 162, 184, 0.1);
            color: #17a2b8;
            border: 1px solid rgba(23, 162, 184, 0.2);
        }
        .warning { 
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.2);
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        .metric-card.pass {
            border-color: rgba(40, 167, 69, 0.5);
            background: rgba(40, 167, 69, 0.1);
        }
        .metric-card.fail {
            border-color: rgba(220, 53, 69, 0.5);
            background: rgba(220, 53, 69, 0.1);
        }
        .metric-value {
            font-size: 28px;
            font-weight: bold;
            margin: 10px 0;
            background: linear-gradient(135deg, #8A2BE2, #9B4DEE);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .metric-label {
            font-size: 14px;
            color: #444;
            font-weight: 500;
        }
        .requirement {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .log {
            background: rgba(45, 55, 72, 0.95);
            color: #e2e8f0;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Menlo', 'Monaco', monospace;
            font-size: 13px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 zkTLS 证明条件测试工具</h1>
        <p>调整和测试 zkTLS 证明生成的条件要求</p>

        <div class="grid">
            <!-- 左侧：配置调整 -->
            <div>
                <div class="section">
                    <h3>⚙️ 条件配置</h3>
                    <div class="form-group">
                        <label>最低分数要求:</label>
                        <input type="number" id="min-score" value="75" min="0" max="100">
                    </div>
                    <div class="form-group">
                        <label>最低持续时间 (秒):</label>
                        <input type="number" id="min-duration" value="3" min="0" max="30" step="0.1">
                    </div>
                    <div class="form-group">
                        <label>最低准确度 (%):</label>
                        <input type="number" id="min-accuracy" value="80" min="0" max="100">
                    </div>
                    <button id="update-config" class="btn">更新配置</button>
                    <button id="reset-config" class="btn">重置默认</button>
                </div>

                <div class="section">
                    <h3>🧪 模拟测试</h3>
                    <div class="form-group">
                        <label>测试分数:</label>
                        <input type="number" id="test-score" value="85" min="0" max="100">
                    </div>
                    <div class="form-group">
                        <label>测试持续时间 (秒):</label>
                        <input type="number" id="test-duration" value="5" min="0" max="30" step="0.1">
                    </div>
                    <div class="form-group">
                        <label>测试准确度 (%):</label>
                        <input type="number" id="test-accuracy" value="90" min="0" max="100">
                    </div>
                    <button id="test-conditions" class="btn">测试条件</button>
                    <button id="generate-test-data" class="btn">生成随机数据</button>
                </div>
            </div>

            <!-- 右侧：结果显示 -->
            <div>
                <div class="section">
                    <h3>📊 测试结果</h3>
                    <div id="test-status" class="status info">等待测试...</div>
                    
                    <div class="metrics">
                        <div id="score-metric" class="metric-card">
                            <div class="metric-label">分数</div>
                            <div id="score-value" class="metric-value">0</div>
                            <div class="requirement">要求: ≥<span id="score-req">75</span></div>
                        </div>
                        <div id="duration-metric" class="metric-card">
                            <div class="metric-label">持续时间</div>
                            <div id="duration-value" class="metric-value">0s</div>
                            <div class="requirement">要求: ≥<span id="duration-req">3</span>s</div>
                        </div>
                        <div id="accuracy-metric" class="metric-card">
                            <div class="metric-label">准确度</div>
                            <div id="accuracy-value" class="metric-value">0%</div>
                            <div class="requirement">要求: ≥<span id="accuracy-req">80</span>%</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h3>💡 建议</h3>
                    <div id="suggestions" class="info">
                        <p>调整测试参数以查看建议...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>📝 操作日志</h3>
            <div id="log" class="log">等待操作...</div>
        </div>
    </div>

    <!-- 加载依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/ethers@5.7.2/dist/ethers.umd.min.js"></script>
    <script src="js/zktls-browser-wrapper.js"></script>
    <script src="js/zktls-config.js"></script>

    <script>
        let currentConfig = {
            minScore: 75,
            minDuration: 3,
            minAccuracy: 80
        };

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('test-status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function updateMetrics(testData, requirements) {
            // 更新分数
            const scorePass = testData.score >= requirements.minScore;
            document.getElementById('score-value').textContent = testData.score;
            document.getElementById('score-req').textContent = requirements.minScore;
            document.getElementById('score-metric').className = `metric-card ${scorePass ? 'pass' : 'fail'}`;

            // 更新持续时间
            const durationPass = testData.duration >= requirements.minDuration;
            document.getElementById('duration-value').textContent = testData.duration + 's';
            document.getElementById('duration-req').textContent = requirements.minDuration;
            document.getElementById('duration-metric').className = `metric-card ${durationPass ? 'pass' : 'fail'}`;

            // 更新准确度
            const accuracyPass = testData.accuracy >= requirements.minAccuracy;
            document.getElementById('accuracy-value').textContent = testData.accuracy + '%';
            document.getElementById('accuracy-req').textContent = requirements.minAccuracy;
            document.getElementById('accuracy-metric').className = `metric-card ${accuracyPass ? 'pass' : 'fail'}`;

            return { scorePass, durationPass, accuracyPass };
        }

        function updateSuggestions(testData, requirements, results) {
            const suggestions = [];
            
            if (!results.scorePass) {
                const needed = requirements.minScore - testData.score;
                suggestions.push(`🎯 分数需要提高 ${needed.toFixed(1)} 分`);
                suggestions.push(`💡 建议：提高姿势准确度和稳定性`);
            }

            if (!results.durationPass) {
                const needed = requirements.minDuration - testData.duration;
                suggestions.push(`⏱️ 持续时间需要增加 ${needed.toFixed(1)} 秒`);
                suggestions.push(`💡 建议：保持姿势稳定，避免频繁移动`);
            }

            if (!results.accuracyPass) {
                const needed = requirements.minAccuracy - testData.accuracy;
                suggestions.push(`🎯 准确度需要提高 ${needed.toFixed(1)}%`);
                suggestions.push(`💡 建议：调整身体位置，确保关键点检测准确`);
            }

            if (suggestions.length === 0) {
                suggestions.push('🎉 所有条件都已满足！可以生成 zkTLS 证明了！');
                suggestions.push('✨ 当前配置适合实际使用');
            } else {
                // 添加配置建议
                if (testData.score < 60) {
                    suggestions.push('⚙️ 考虑降低最低分数要求到 60-70 分');
                }
                if (testData.duration < 2) {
                    suggestions.push('⚙️ 考虑降低最低持续时间要求到 2 秒');
                }
                if (testData.accuracy < 70) {
                    suggestions.push('⚙️ 考虑降低最低准确度要求到 70%');
                }
            }

            document.getElementById('suggestions').innerHTML = 
                suggestions.map(s => `<p>${s}</p>`).join('');
        }

        // 更新配置
        document.getElementById('update-config').addEventListener('click', () => {
            currentConfig.minScore = parseFloat(document.getElementById('min-score').value);
            currentConfig.minDuration = parseFloat(document.getElementById('min-duration').value);
            currentConfig.minAccuracy = parseFloat(document.getElementById('min-accuracy').value);

            // 更新全局配置
            if (window.ZKTLSConfig) {
                window.ZKTLSConfig.conditions.minScore = currentConfig.minScore;
                window.ZKTLSConfig.conditions.minDuration = currentConfig.minDuration;
                window.ZKTLSConfig.conditions.minAccuracy = currentConfig.minAccuracy;
            }

            log(`配置已更新: 分数≥${currentConfig.minScore}, 时间≥${currentConfig.minDuration}s, 准确度≥${currentConfig.minAccuracy}%`);
            updateStatus('配置已更新', 'success');
        });

        // 重置配置
        document.getElementById('reset-config').addEventListener('click', () => {
            document.getElementById('min-score').value = 75;
            document.getElementById('min-duration').value = 3;
            document.getElementById('min-accuracy').value = 80;
            
            currentConfig = { minScore: 75, minDuration: 3, minAccuracy: 80 };
            log('配置已重置为默认值');
            updateStatus('配置已重置', 'info');
        });

        // 测试条件
        document.getElementById('test-conditions').addEventListener('click', () => {
            const testData = {
                score: parseFloat(document.getElementById('test-score').value),
                duration: parseFloat(document.getElementById('test-duration').value),
                accuracy: parseFloat(document.getElementById('test-accuracy').value)
            };

            log(`测试数据: 分数=${testData.score}, 时间=${testData.duration}s, 准确度=${testData.accuracy}%`);

            const results = updateMetrics(testData, currentConfig);
            const allPass = results.scorePass && results.durationPass && results.accuracyPass;

            if (allPass) {
                updateStatus('✅ 所有条件都满足！可以生成证明', 'success');
                log('✅ 测试通过：满足所有证明条件');
            } else {
                const failed = [];
                if (!results.scorePass) failed.push('分数');
                if (!results.durationPass) failed.push('持续时间');
                if (!results.accuracyPass) failed.push('准确度');
                
                updateStatus(`❌ 不满足条件: ${failed.join(', ')}`, 'error');
                log(`❌ 测试失败：不满足 ${failed.join(', ')} 要求`);
            }

            updateSuggestions(testData, currentConfig, results);
        });

        // 生成随机测试数据
        document.getElementById('generate-test-data').addEventListener('click', () => {
            const randomScore = Math.floor(Math.random() * 100);
            const randomDuration = Math.round((Math.random() * 10 + 1) * 10) / 10;
            const randomAccuracy = Math.floor(Math.random() * 100);

            document.getElementById('test-score').value = randomScore;
            document.getElementById('test-duration').value = randomDuration;
            document.getElementById('test-accuracy').value = randomAccuracy;

            log(`生成随机测试数据: 分数=${randomScore}, 时间=${randomDuration}s, 准确度=${randomAccuracy}%`);
            
            // 自动测试
            document.getElementById('test-conditions').click();
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('zkTLS 条件测试工具已加载');
            updateStatus('工具已就绪，请调整参数进行测试', 'info');
            
            // 初始化显示
            updateMetrics(
                { score: 0, duration: 0, accuracy: 0 },
                currentConfig
            );
        });
    </script>
</body>
</html>
