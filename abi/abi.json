[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "string", "name": "achievement", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "AchievementUnlocked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "funder", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "ContractFunded", "type": "event"}, {"inputs": [], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "string", "name": "<PERSON><PERSON><PERSON>", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "newRecord", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "NewRecord", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "string", "name": "<PERSON><PERSON><PERSON>", "type": "string"}, {"indexed": false, "internalType": "enum RhythmPoseProof.PoseType", "name": "poseType", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "score", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "duration", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "zkProof", "type": "string"}], "name": "PoseRecorded", "type": "event"}, {"inputs": [{"internalType": "string", "name": "_poseName", "type": "string"}, {"internalType": "enum RhythmPoseProof.PoseType", "name": "_poseType", "type": "uint8"}, {"internalType": "uint256", "name": "_score", "type": "uint256"}, {"internalType": "uint256", "name": "_duration", "type": "uint256"}], "name": "recordPose", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_poseName", "type": "string"}, {"internalType": "enum RhythmPoseProof.PoseType", "name": "_poseType", "type": "uint8"}, {"internalType": "uint256", "name": "_score", "type": "uint256"}, {"internalType": "uint256", "name": "_duration", "type": "uint256"}, {"internalType": "string", "name": "_zkProof", "type": "string"}], "name": "recordVerified<PERSON>ose", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "RewardDistributed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "RewardsToggled", "type": "event"}, {"inputs": [], "name": "toggleRewards", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_newOwner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "withdrawBalance", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}, {"inputs": [], "name": "getContractBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getContractStats", "outputs": [{"internalType": "uint256", "name": "totalUsers", "type": "uint256"}, {"internalType": "uint256", "name": "totalRecordsCount", "type": "uint256"}, {"internalType": "address", "name": "contractOwner", "type": "address"}, {"internalType": "uint256", "name": "contractBalance", "type": "uint256"}, {"internalType": "uint256", "name": "totalRewards", "type": "uint256"}, {"internalType": "bool", "name": "rewardsStatus", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_poseName", "type": "string"}], "name": "getPoseLeaderboard", "outputs": [{"internalType": "uint256", "name": "bestScore", "type": "uint256"}, {"internalType": "address", "name": "champion", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getRewardConfig", "outputs": [{"internalType": "uint256", "name": "rewardAmount", "type": "uint256"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "getUserAchievement", "outputs": [{"internalType": "uint256", "name": "totalPoses", "type": "uint256"}, {"internalType": "uint256", "name": "bestScore", "type": "uint256"}, {"internalType": "uint256", "name": "totalDuration", "type": "uint256"}, {"internalType": "uint256", "name": "level", "type": "uint256"}, {"internalType": "uint256", "name": "experience", "type": "uint256"}, {"internalType": "uint256", "name": "totalRewards", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "uint256", "name": "_index", "type": "uint256"}], "name": "getUserPoseRecord", "outputs": [{"internalType": "string", "name": "<PERSON><PERSON><PERSON>", "type": "string"}, {"internalType": "enum RhythmPoseProof.PoseType", "name": "poseType", "type": "uint8"}, {"internalType": "uint256", "name": "score", "type": "uint256"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "bool", "name": "verified", "type": "bool"}, {"internalType": "string", "name": "zkProof", "type": "string"}, {"internalType": "bool", "name": "rewarded", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "getUserRecordCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "name": "poseChampions", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "name": "poseLeaderboard", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REWARD_AMOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardsEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalRecords", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalRewardsDistributed", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "userAchievements", "outputs": [{"internalType": "uint256", "name": "totalPoses", "type": "uint256"}, {"internalType": "uint256", "name": "bestScore", "type": "uint256"}, {"internalType": "uint256", "name": "totalDuration", "type": "uint256"}, {"internalType": "uint256", "name": "level", "type": "uint256"}, {"internalType": "uint256", "name": "experience", "type": "uint256"}, {"internalType": "uint256", "name": "totalRewards", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "userPoseRecords", "outputs": [{"internalType": "string", "name": "<PERSON><PERSON><PERSON>", "type": "string"}, {"internalType": "enum RhythmPoseProof.PoseType", "name": "poseType", "type": "uint8"}, {"internalType": "uint256", "name": "score", "type": "uint256"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "bool", "name": "verified", "type": "bool"}, {"internalType": "string", "name": "zkProof", "type": "string"}, {"internalType": "bool", "name": "rewarded", "type": "bool"}], "stateMutability": "view", "type": "function"}]