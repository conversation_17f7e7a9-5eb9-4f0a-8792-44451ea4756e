<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>奖励发放确认</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            text-align: center;
        }
        .success-icon {
            font-size: 80px;
            margin: 20px 0;
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        .reward-amount {
            font-size: 48px;
            font-weight: bold;
            color: #f1c40f;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin: 20px 0;
        }
        .info-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .status-value {
            font-weight: bold;
            color: #f1c40f;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">🎉</div>
        <h1>奖励发放成功确认</h1>
        <p>恭喜！你的rhythm-pose项目奖励系统现在可以正常发放代币了！</p>
        
        <div class="reward-amount">0.01 S</div>
        <p id="rewardMessage">每次成功记录验证姿势后，用户将自动获得奖励</p>

        <div id="transactionDetails" class="info-box" style="display: none;">
            <h3>🎯 本次交易详情</h3>
            <div class="status-item">
                <span>姿势名称</span>
                <span class="status-value" id="poseName">-</span>
            </div>
            <div class="status-item">
                <span>获得分数</span>
                <span class="status-value" id="poseScore">-</span>
            </div>
            <div class="status-item">
                <span>交易哈希</span>
                <span class="status-value" id="txHash">-</span>
            </div>
            <div class="status-item">
                <span>奖励金额</span>
                <span class="status-value">0.01 S</span>
            </div>
        </div>

        <div class="info-box">
            <h3>✅ 修复完成的问题</h3>
            <div class="status-item">
                <span>Gas价格转换错误</span>
                <span class="status-value">已修复</span>
            </div>
            <div class="status-item">
                <span>未定义变量错误</span>
                <span class="status-value">已修复</span>
            </div>
            <div class="status-item">
                <span>备用交易功能</span>
                <span class="status-value">正常工作</span>
            </div>
            <div class="status-item">
                <span>奖励发放机制</span>
                <span class="status-value">可以发送</span>
            </div>
        </div>

        <div class="info-box">
            <h3>🔧 技术修复详情</h3>
            <ul style="text-align: left;">
                <li><strong>Gas计算修复</strong>: 使用 <code>Math.floor()</code> 确保整数值</li>
                <li><strong>变量作用域修复</strong>: 将 <code>poseData</code> 提取到函数开始处</li>
                <li><strong>错误处理改进</strong>: 备用交易现在可以正确访问姿势数据</li>
                <li><strong>奖励系统验证</strong>: 确认合约余额和奖励配置正确</li>
            </ul>
        </div>

        <div class="info-box">
            <h3>🚀 下一步建议</h3>
            <ul style="text-align: left;">
                <li>测试完整的姿势记录流程，确认奖励正常发放</li>
                <li>监控合约余额，确保有足够资金持续发放奖励</li>
                <li>考虑添加奖励发放的用户界面通知</li>
                <li>为hackathon演示准备奖励功能的展示</li>
            </ul>
        </div>

        <button class="button" onclick="window.open('check-rewards.html', '_blank')">
            查看详细奖励状态
        </button>
        
        <button class="button" onclick="window.open('test-blockchain-fix.html', '_blank')">
            运行技术测试
        </button>

        <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
            <p>🎯 <strong>合约地址</strong>: 0x057499692a5E14C19Fb9e4274d3e9C6Fa4ECb560</p>
            <p>🌐 <strong>网络</strong>: Sonic Blaze Testnet (Chain ID: 57054)</p>
            <p>⚡ <strong>状态</strong>: 奖励系统正常运行</p>
        </div>
    </div>

    <script>
        // Add some celebration effects
        function createConfetti() {
            const colors = ['#f1c40f', '#e74c3c', '#3498db', '#2ecc71', '#9b59b6'];
            
            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.style.cssText = `
                        position: fixed;
                        top: -10px;
                        left: ${Math.random() * 100}vw;
                        width: 10px;
                        height: 10px;
                        background: ${colors[Math.floor(Math.random() * colors.length)]};
                        border-radius: 50%;
                        pointer-events: none;
                        animation: fall 3s linear forwards;
                        z-index: 1000;
                    `;
                    
                    document.body.appendChild(confetti);
                    
                    setTimeout(() => {
                        confetti.remove();
                    }, 3000);
                }, i * 100);
            }
        }

        // Add CSS for falling animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fall {
                to {
                    transform: translateY(100vh) rotate(360deg);
                }
            }
        `;
        document.head.appendChild(style);

        // Parse URL parameters and display transaction details
        function parseURLParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const txHash = urlParams.get('tx');
            const poseName = urlParams.get('pose');
            const score = urlParams.get('score');

            if (txHash && poseName && score) {
                // Update message
                document.getElementById('rewardMessage').textContent = '恭喜！你刚刚获得了奖励！';

                // Show transaction details
                document.getElementById('transactionDetails').style.display = 'block';
                document.getElementById('poseName').textContent = decodeURIComponent(poseName);
                document.getElementById('poseScore').textContent = score;
                document.getElementById('txHash').innerHTML = `
                    <a href="https://blaze.soniclabs.com/tx/${txHash}" target="_blank" style="color: #f1c40f; text-decoration: none;">
                        ${txHash.substring(0, 10)}...${txHash.substring(txHash.length - 8)}
                    </a>
                `;

                // Add extra celebration for successful transaction
                setTimeout(() => {
                    createConfetti();
                    setTimeout(createConfetti, 1000);
                }, 500);
            }
        }

        // Start confetti when page loads
        window.addEventListener('load', () => {
            parseURLParams();
            setTimeout(createConfetti, 500);
        });
    </script>
</body>
</html>
